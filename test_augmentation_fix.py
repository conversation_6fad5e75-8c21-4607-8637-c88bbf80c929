#!/usr/bin/env python3
"""
Test script to verify that SafeCoarseDropout works with eye coordinates.
"""

import numpy as np
import cv2
from utils.augmentation import get_gaze_safe_augmentations

def test_safe_coarse_dropout():
    """Test that SafeCoarseDropout works with eye coordinates."""
    
    # Create a simple test image
    image = np.random.randint(0, 255, (96, 96, 3), dtype=np.uint8)
    
    # Define eye coordinates (normalized)
    eye_coords = [(0.3, 0.4), (0.7, 0.4)]  # left eye, right eye
    
    # Create the transform
    transform = get_gaze_safe_augmentations()
    
    try:
        # Apply the transform
        result = transform(image=image, eye_coords=eye_coords)
        
        print("✅ SafeCoarseDropout test passed!")
        print(f"Original image shape: {image.shape}")
        print(f"Transformed image shape: {result['image'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ SafeCoarseDropout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_safe_coarse_dropout()
    exit(0 if success else 1)

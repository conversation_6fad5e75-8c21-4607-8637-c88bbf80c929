import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import timm 
from torch.nn import Module, Parameter
# from yolov8 import YOLOv8

def l2_norm(input, axis=1):
    norm = torch.norm(input, 2, axis, True)
    output = torch.div(input, norm)
    return output

class AdaFace(Module):
    def __init__(self, embedding_size=512, classnum=28, m=0.4, h=0.333, s=64., t_alpha=1.0):
        super(AdaFace, self).__init__()
        self.classnum = classnum
        self.kernel = Parameter(torch.Tensor(embedding_size, classnum))

        # Initial kernel
        self.kernel.data.uniform_(-1, 1).renorm_(2, 1, 1e-5).mul_(1e5)
        self.m = m 
        self.eps = 1e-3
        self.h = h
        self.s = s

        # EMA prep
        self.t_alpha = t_alpha
        self.register_buffer('t', torch.zeros(1))
        self.register_buffer('batch_mean', torch.ones(1) * 20)
        self.register_buffer('batch_std', torch.ones(1) * 100)

    def forward(self, embeddings, norms, label):
        kernel_norm = l2_norm(self.kernel, axis=0)
        cosine = torch.mm(embeddings, kernel_norm)
        cosine = cosine.clamp(-1 + self.eps, 1 - self.eps)  # Stability

        safe_norms = torch.clip(norms, min=0.001, max=100)  # Stability
        safe_norms = safe_norms.clone().detach()

        # Update batch mean and std
        with torch.no_grad():
            mean = safe_norms.mean().detach()
            std = safe_norms.std().detach()
            self.batch_mean = mean * self.t_alpha + (1 - self.t_alpha) * self.batch_mean
            self.batch_std = std * self.t_alpha + (1 - self.t_alpha) * self.batch_std

        margin_scaler = (safe_norms - self.batch_mean) / (self.batch_std + self.eps)  # 66% between -1, 1
        margin_scaler = margin_scaler * self.h  # 68% between -0.333 ,0.333 when h:0.333
        margin_scaler = torch.clip(margin_scaler, -1, 1)

        # g_angular
        m_arc = torch.zeros(label.size()[0], cosine.size()[1], device=cosine.device)
        m_arc.scatter_(1, label.reshape(-1, 1), 1.0)
        g_angular = self.m * margin_scaler * -1
        m_arc = m_arc * g_angular
        theta = cosine.acos()
        theta_m = torch.clip(theta + m_arc, min=self.eps, max=math.pi - self.eps)
        cosine = theta_m.cos()

        # g_additive
        m_cos = torch.zeros(label.size()[0], cosine.size()[1], device=cosine.device)
        m_cos.scatter_(1, label.reshape(-1, 1), 1.0)
        g_add = self.m + (self.m * margin_scaler)
        m_cos = m_cos * g_add
        cosine = cosine - m_cos

        # Scale
        scaled_cosine_m = cosine * self.s
        return scaled_cosine_m

class L2CS_ADA(nn.Module):
    def __init__(self, block, layers, num_bins):
        self.inplanes = 64
        super(L2CS_ADA, self).__init__()
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2)
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # AdaFace heads for training
        self.adaface_yaw = AdaFace(embedding_size=512 * block.expansion, classnum=num_bins)
        self.adaface_pitch = AdaFace(embedding_size=512 * block.expansion, classnum=num_bins)

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = nn.Linear(512 * block.expansion, num_bins)
        self.fc_pitch_gaze = nn.Linear(512 * block.expansion, num_bins)
        
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x, labels=None, training = True):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        x = self.avgpool(x)
        x = x.view(x.size(0), -1)

        # Normalize embeddings
        norms = torch.norm(x, 2, 1, True)
        normalized_x = l2_norm(x)

        if training:
            # AdaFace heads for training
            pre_yaw_gaze_adaface = self.adaface_yaw(normalized_x, norms, labels[:, 0])
            pre_pitch_gaze_adaface = self.adaface_pitch(normalized_x, norms, labels[:, 1])

            # Fully connected layers for simultaneous training
            pre_yaw_gaze_fc = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze_fc = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze_adaface, pre_pitch_gaze_adaface, pre_yaw_gaze_fc, pre_pitch_gaze_fc
        else:
            # Fully connected layers for inference using the same normalized embeddings
            pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze, pre_pitch_gaze


class MOBILEV4_ADA_small(Module):
    def __init__(self, embedding_size = 1280, num_bins = 28):
        super(MOBILEV4_ADA_small, self).__init__()

        # Loading the pre-trained MobileNetV4 model. 
        self.backbone = timm.create_model(
            "mobilenetv4_conv_small.e2400_r224_in1k", pretrained= True, num_classes = 0
        )
        self.adaface_yaw = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )
        self.adaface_pitch = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
        self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
    def forward(self, x, labels = None, training = True):
        x = self.backbone(x) 
        norms = torch.norm(x, 2, 1, True)
        normalized_x = l2_norm(x) 

        if training:
            # AdaFace heads for training
            pre_yaw_gaze_adaface = self.adaface_yaw(normalized_x, norms, labels[:, 0])
            pre_pitch_gaze_adaface = self.adaface_pitch(
                normalized_x, norms, labels[:, 1]
            )

            # Fully connected layers for simultaneous training
            pre_yaw_gaze_fc = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze_fc = self.fc_pitch_gaze(normalized_x)

            return (
                pre_yaw_gaze_adaface,
                pre_pitch_gaze_adaface,
                pre_yaw_gaze_fc,
                pre_pitch_gaze_fc,
            )
        else:
            # Fully connected layers for inference using the same normalized embeddings
            pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze, pre_pitch_gaze



class MOBILEV4_ADA(Module):
    def __init__(self, embedding_size = 1280, num_bins = 28):
        super(MOBILEV4_ADA, self).__init__()

        # Loading the pre-trained MobileNetV4 model. 
        self.backbone = timm.create_model(
            "mobilenetv4_conv_medium.e500_r224_in1k", pretrained= True, num_classes = 0
        )
        self.adaface_yaw = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )
        self.adaface_pitch = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
        self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
    
    # def forward(self, x, labels = None, training = True):  # Use to train the model
    def forward(self, x, labels = None, training = False):   # Use to test the model
        x = self.backbone(x) 
        norms = torch.norm(x, 2, 1, True)
        normalized_x = l2_norm(x) 

        if training:
            # AdaFace heads for training
            pre_yaw_gaze_adaface = self.adaface_yaw(normalized_x, norms, labels[:, 0])
            pre_pitch_gaze_adaface = self.adaface_pitch(
                normalized_x, norms, labels[:, 1]
            )

            # Fully connected layers for simultaneous training
            pre_yaw_gaze_fc = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze_fc = self.fc_pitch_gaze(normalized_x)

            return (
                pre_yaw_gaze_adaface,
                pre_pitch_gaze_adaface,
                pre_yaw_gaze_fc,
                pre_pitch_gaze_fc,
            )
        else:
            # Fully connected layers for inference using the same normalized embeddings
            pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze, pre_pitch_gaze
        
        

# MobileNetV4 with AdaFace heads for converting pt to onnx

# class MOBILEV4_ADA(Module):
#     def __init__(self, embedding_size = 1280, num_bins = 28):
#         super(MOBILEV4_ADA, self).__init__()

#         # Loading the pre-trained MobileNetV4 model. 
#         self.backbone = timm.create_model(
#             "mobilenetv4_conv_medium.e500_r224_in1k", pretrained= True, num_classes = 0
#         )
#         self.adaface_yaw = AdaFace(
#             embedding_size= embedding_size, classnum= num_bins
#         )
#         self.adaface_pitch = AdaFace(
#             embedding_size= embedding_size, classnum= num_bins
#         )

#         # Fully connected layers for gaze estimation
#         self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
#         self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
#     def forward(self, x):
#         x = self.backbone(x) 
#         normalized_x = l2_norm(x) 

#         # Fully connected layers for inference using the same normalized embeddings
#         pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
#         pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

#         return pre_yaw_gaze, pre_pitch_gaze
            


class MOBILEV4_ADA_v2(Module):
    def __init__(self, embedding_size = 1280, num_bins = 28):
        super(MOBILEV4_ADA_v2, self).__init__()

        # Loading the pre-trained MobileNetV4 model. 
        self.backbone = timm.create_model(
            "mobilenetv4_conv_medium.e500_r224_in1k", pretrained= True, num_classes = 0 
        )
        self.adaface_yaw = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )
        self.adaface_pitch = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
        self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
    def forward(self, x, labels = None, training = True, epoch = 0):
        x = self.backbone(x) 
        norms = torch.norm(x, 2, 1, True)
        normalized_x = l2_norm(x) 

        if training and epoch < 72:
            # AdaFace heads for training
            pre_yaw_gaze_adaface = self.adaface_yaw(normalized_x, norms, labels[:, 0])
            pre_pitch_gaze_adaface = self.adaface_pitch(
                normalized_x, norms, labels[:, 1]
            )

            # Fully connected layers for simultaneous training
            pre_yaw_gaze_fc = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze_fc = self.fc_pitch_gaze(normalized_x)

            return (
                pre_yaw_gaze_adaface,
                pre_pitch_gaze_adaface,
                pre_yaw_gaze_fc,
                pre_pitch_gaze_fc,
            )
        else:
            # Fully connected layers for inference using the same normalized embeddings
            pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze, pre_pitch_gaze        
        

class MOBILEV4_ADA_v3(Module):
    def __init__(self, embedding_size = 1280, num_bins = 28):
        super(MOBILEV4_ADA_v3, self).__init__()

        # Loading the pre-trained MobileNetV4 model. 
        self.backbone = timm.create_model(
            "mobilenetv4_conv_medium.e500_r224_in1k", pretrained= True, num_classes = 0
        )
        self.adaface_yaw = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )
        self.adaface_pitch = AdaFace(
            embedding_size= embedding_size, classnum= num_bins
        )

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
        self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
    def forward(self, x, labels = None, training = True):
        x = self.backbone(x) 
        norms = torch.norm(x, 2, 1, True)
        normalized_x = l2_norm(x) 

        if training:
            # AdaFace heads for training
            pre_yaw_gaze_adaface = self.adaface_yaw(normalized_x, norms, labels[:, 0])
            pre_pitch_gaze_adaface = self.adaface_pitch(
                normalized_x, norms, labels[:, 1]
            )

            # Fully connected layers for simultaneous training
            pre_yaw_gaze_fc = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze_fc = self.fc_pitch_gaze(normalized_x)

            return (
                pre_yaw_gaze_adaface,
                pre_pitch_gaze_adaface,
                pre_yaw_gaze_fc,
                pre_pitch_gaze_fc,
            )
        else:
            # Fully connected layers for inference using the same normalized embeddings
            pre_yaw_gaze = self.fc_yaw_gaze(normalized_x)
            pre_pitch_gaze = self.fc_pitch_gaze(normalized_x)

            return pre_yaw_gaze, pre_pitch_gaze
        
                
class MOBILEV4(Module):
    def __init__(self, embedding_size=1280, num_bins=28):
        super(MOBILEV4, self).__init__()

        # Loading the pre-trained MobileNetV4 model. 
        self.backbone = timm.create_model(
            "mobilenetv4_conv_medium.e500_r224_in1k", pretrained=True, num_classes=0
        )

        # Fully connected layers for gaze estimation
        self.fc_yaw_gaze = torch.nn.Linear(embedding_size, num_bins)
        self.fc_pitch_gaze = torch.nn.Linear(embedding_size, num_bins)
        
    def forward(self, x):
        x = self.backbone(x) 

        yaw = self.fc_yaw_gaze(x)
        pitch = self.fc_pitch_gaze(x)

        return pitch, yaw
    
    
    
# class YoloV8(Module):
#     def __init__(self, embedding_size = 1280, num_bins = 28):
#         super(YoloV8, self).__init__()

#         # Loading the pre-trained MobileNetV4 model. 
#         self.backbone = YOLOv8(architecture="tiyolov8s", pretrained= True, finetune=True)
#         internal_embedding_size = self.backbone.dimList[-1]
        
#         # Fully connected layers for gaze estimation
#         self.fc_yaw_gaze = torch.nn.Linear(internal_embedding_size, num_bins)
#         self.fc_pitch_gaze = torch.nn.Linear(internal_embedding_size, num_bins)
#         self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
#     def forward(self, x, labels = None, training = True):
#         x = self.backbone(x)
#         x = self.avgpool(x)
#         x = x.view(x.size(0), -1)
#         # Fully connected layers for inference using the same normalized embeddings
#         pre_yaw_gaze = self.fc_yaw_gaze(x)
#         pre_pitch_gaze = self.fc_pitch_gaze(x)

#         return pre_yaw_gaze, pre_pitch_gaze
    
    
if __name__ == "__main__":
    # mobile = YoloV8()
    mobile = MOBILEV4()
    mobile.eval()
    yaw, pitch = mobile(torch.randn(1, 3, 224, 224))
    print(yaw.shape, pitch.shape)
    print(yaw, pitch)
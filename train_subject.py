# === Standard Libraries ===
import os
import logging
import argparse
from tqdm import tqdm

# === Third-Party Libraries ===
import torch
import torch.nn as nn

# === Local Modules ===
from utils import util
from utils.datasets_subject import get_dataloaders
from utils.helpers import calculate_gaze_angle_error
from models.vgg_face import FinalModel


util.setup_multi_processes()
util.init_deterministic_seed()


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--data",           type=str,   default="data/dataset_normalized_merged_v1-v2-v3_GT_EYE_CENTERED")
    parser.add_argument("--save-dir",       type=str,   default="runs")
    parser.add_argument("--weights",        type=str,   default="runs/exp_?/best_model.pt")
    parser.add_argument("--num-epochs",     type=int,   default=100)
    parser.add_argument("--batch-size",     type=int,   default=32)
    parser.add_argument("--lr",             type=float, default=0.001)
    parser.add_argument("--num-workers",    type=int,   default=8)
    args = parser.parse_args()
    return args


def backup():
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    # =============================================================================================
    # import torchvision.models as models
    
    # class GazeRegNet(nn.Module):
    #     def __init__(self, num_bins=20):
    #         super().__init__()
    #         # Load pretrained RegNet model
    #         backbone = models.get_model(name="regnet_x_800mf", weights=True)
            
    #         # Save only the feature extractor
    #         self.backbone = nn.Sequential(
    #             backbone.stem,         # stem
    #             backbone.trunk_output, # conv blocks
    #             backbone.avgpool       # AdaptiveAvgPool2d(output_size=(1, 1))
    #         )

    #         self.flatten = nn.Flatten()

    #         # RegNet_x_800mf outputs 672-dim features after avgpool
    #         self.gaze = nn.Linear(672, 2)
            
    #         self._init_weights()

    #     def _init_weights(self):
    #         """Initialize weights for the regression layers."""
    #         # Initialize the regression layer
    #         torch.nn.init.normal_(self.gaze.weight, std=0.001)
    #         if self.gaze.bias is not None:
    #             torch.nn.init.constant_(self.gaze.bias, 0)

    #     def forward(self, x):
    #         x = self.backbone(x)   # (B, 672, 1, 1)
    #         x = self.flatten(x)    # (B, 672)
    #         gaze = self.gaze(x)   # (B, 20)
    #         return gaze
    # model = GazeRegNet()
    
    # model = models.get_model(name="regnet_x_800mf", weights=True)
    
    # Prepare model
    # model = get_model(args.arch, args.bins, pretrained=True)
    # model = get_model(args.arch, args.bins, pretrained=True)

    # util.freeze_batchnorm_layers(model)

    # Freeze stem part via param.requires_grad
    # for param in util.get_params(model, part="stem", require_grad_only=False):
    #     param.requires_grad = False
    
    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="stem", require_grad_only=True), 'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="layer1", require_grad_only=True), 'lr': args.lr*0.7},
    #     {'params': util.get_params(model, part="layer2", require_grad_only=True), 'lr': args.lr*0.8},
    #     {'params': util.get_params(model, part="layer3", require_grad_only=True), 'lr': args.lr*0.9},
    #     {'params': util.get_params(model, part="layer4", require_grad_only=True), 'lr': args.lr*0.9},
    #     # {'params': util.get_params(model, part="backbone", require_grad_only=True), 'lr': args.lr},
    #     {'params': util.get_params(model, part="head", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))

    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="cnn_face", require_grad_only=True),     'lr': args.lr},
    #     # {'params': util.get_params(model, part="cnn_eye", require_grad_only=True),      'lr': args.lr*0.1},
    #     # {'params': util.get_params(model, part="cnn_eye2fc", require_grad_only=True),   'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="fc_face", require_grad_only=True),      'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eye", require_grad_only=True),       'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eyes_face", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))
    
    # Angular Vector Error: 3.9360886° with *0.1
    # optimizer = torch.optim.AdamW(
    #     model.parameters(), 
    #     lr=args.lr, 
    #     weight_decay=1e-2, 
    #     betas=(0.9, 0.999), 
    #     eps=1e-8
    # )
    
    # scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    #     optimizer,
    #     mode='min',             # or 'max' if tracking accuracy
    #     factor=0.1,             # LR is reduced by this factor
    #     patience=5,             # Wait this many epochs with no improvement
    #     threshold=1e-4,         # Minimal change to be considered an improvement
    #     cooldown=0,             # Number of epochs to wait after LR reduction
    #     min_lr=1e-7,            # Minimum LR allowed
    # )
    # scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs)
    # scheduler = torch.optim.lr_scheduler.ExponentialLR(optimizer, gamma=0.9)
    # scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    # =============================================================================================
    return


def initialize_model(args, device):
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    model = FinalModel()
    model_dict = model.state_dict()

    best_model_path = "weights/author_subject-independent.ckpt"
    snapshot = torch.load(best_model_path, map_location=device, weights_only=True)

    snapshot = {k: v for k, v in snapshot["state_dict"].items() if k in model_dict and v.shape == model_dict[k].shape}
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)
    torch.save({"state_dict": model.state_dict(), "pytorch-lightning_version": '2.5.0'},
               "weights/author_subject-independent_face.ckpt")
    
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=0.0,
        eps=1e-8,           # Prevent division by zero
        betas=(0.9, 0.999)  # Standard Adam parameters
    )
    
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=10,
        min_lr=1e-7
    )
    
    return model.to(device), optimizer, scheduler


def train_one_epoch(model, reg_criterion, optimizer, data_loader, device):
    """
    Train the model for one epoch.

    Args:
        model (nn.Module): The gaze estimation model.
        reg_criterion (nn.Module): Loss function for regression.
        optimizer (torch.optim.Optimizer): Optimizer for the model.
        data_loader (DataLoader): DataLoader for the training dataset.
        device (torch.device): Device to perform training on.

    Returns:
        Tuple[float, float]: Average losses for pitch and yaw.
    """
    model.train()
    sum_loss = 0
    sum_angular_error = 0
    total_num_samples = 0
    num_batches = len(data_loader)
    
    for images, labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        labels = labels.to(device)

        # Forward pass
        outputs = model(images)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(labels, outputs)

        # Use L1 loss instead of MSE for better stability
        mse_loss = reg_criterion(labels, outputs)

        # Combine losses with safety check
        loss = (loss_ang) + (0.1 * mse_loss)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        # ==================================================================
        
        sum_loss += loss.item() * labels.size(0)
        sum_angular_error += angular_error.item() * labels.size(0)
        total_num_samples += labels.size(0)
    
    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    return avg_loss, avg_angular_error


@torch.no_grad()
def evaluate(model, reg_criterion, data_loader, device, mode="val"):
    """
    Evaluate the model on the test dataset.

    Args:
        model (nn.Module): The gaze estimation model.
        reg_criterion (nn.Module): Loss function for regression.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        device (torch.device): Device to perform evaluation on.
        mode (str): Evaluation mode ("val" or "test").
    """
    model.eval()
    sum_loss = 0
    sum_angular_error = 0
    total_num_samples = 0
    num_batches = len(data_loader)

    for images, labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        labels = labels.to(device)

        # Forward pass
        outputs = model(images)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(labels, outputs)

        # Use L1 loss instead of MSE for better stability
        mse_loss = reg_criterion(labels, outputs)

        # Combine losses with safety check
        loss = (loss_ang) + (0.1 * mse_loss)
        
        sum_loss += loss.item() * labels.size(0)
        sum_angular_error += angular_error.item() * labels.size(0)
        total_num_samples += labels.size(0)

    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"Loss: {avg_loss:.7f}°  |  "
            f"Angular Vector Error: {avg_angular_error:.7f}°"
        )
    
    return avg_loss, avg_angular_error


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    args.save_dir = util.set_experiment_results_output(args, root="runs")
    util.safe_yaml_config_file(args)

    # Initialize model, optimizer, and optionally load a checkpoint
    model, optimizer, scheduler = initialize_model(args, device)

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)

    # Loss functions
    reg_criterion = nn.MSELoss()

    best_model_path = None
    best_error = float('inf')
    performance_results = []

    # Training loop
    for epoch in range(args.num_epochs):
        train_loss, train_angular_error = train_one_epoch(
            model,
            reg_criterion,
            optimizer,
            train_loader,
            device,
        )
        
        # Evaluate on validation set for the current fold
        val_loss, val_angular_error = evaluate(model, reg_criterion, val_loader, device, mode="val")
        
        # Step the scheduler after each epoch
        current_lr = optimizer.param_groups[-1]['lr']
        scheduler.step(val_angular_error)
    
        # Save the best model
        if val_angular_error < best_error:
            best_error = val_angular_error
            best_model_path = os.path.join(args.save_dir, 'best_model.pt')
            torch.save(model.state_dict(), best_model_path)
            status = "*"
        else:
            status = ""

        # Log the results
        logging.info(
            f'Epoch [{epoch + 1}/{args.num_epochs}] '
            f'Losses: {train_loss:.3f} / {val_loss:.3f}  |  '
            f'Angular Vector Error: {train_angular_error:.1f}° / {val_angular_error:.1f}°  |  '
            f'LR: {current_lr:.0e}°  |  {status}'
        )
    
        # Save the results for plotting
        result = (epoch, float(train_loss), float(val_loss), float(train_angular_error), float(val_angular_error))
        performance_results.append(result)
    
    # Plot the results
    util.plot_performance_results(args, performance_results)

    # Test the best model
    if best_model_path is None:
        best_model_path = args.weights
    
    if os.path.exists(best_model_path):
        logging.info(f'Best model saved at {best_model_path}')
        model.load_state_dict(torch.load(best_model_path, map_location=device, weights_only=True))
    else:
        raise ValueError(f"Model weight not found at {best_model_path}")

    model.to(device)
    logging.info("Start Testing")
    evaluate(model, reg_criterion,test_loader, device, mode="test")


if __name__ == '__main__':
    main()

# === Standard Libraries ===
import os

# === Third-Party Libraries ===
import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

# === Local Modules ===
from utils import util


class CustomGazeDataset(Dataset):
    """
    PyTorch Dataset for normalized gaze tracking data.

    This dataset loads face images along with corresponding gaze labels
    from a structured directory format.

    Expected directory structure:
        <data_root>/
        ├── labels.csv
        └── pXX/
            └── <basename>-face.jpg

    CSV format:
        - face_file_name: Relative path to face image
        - pitch: Gaze pitch angle in radians
        - yaw: Gaze yaw angle in radians

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None):
        self.data_path = data_path
        self.transform = transform
        self.df = pd.read_csv(f"{data_path}/labels.csv")

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Load images
        face = skimage.io.imread(f"{self.data_path}/{row.face_file_name}")

        # Apply transforms
        image = self.transform(image=face)["image"]

        # Get labels
        label = torch.stack([torch.tensor(row.pitch, dtype=torch.float32),
                             torch.tensor(row.yaw,   dtype=torch.float32)], dim=0)
        
        return image, label


def get_dataloaders(args, val_split=0.1) -> tuple:
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function automatically splits the training data into train/validation sets
    and creates separate dataloaders with appropriate transforms. Training data
    uses augmentation while validation and test data use only normalization.

    Args:
        - data_root: Root directory containing 'train' and 'test' subdirectories
        - batch_size: Batch size for all dataloaders
        - num_workers: Number of worker processes for data loading

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = args.data
    batch_size = args.batch_size
    num_workers = args.num_workers

    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    transform_train = transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])

    # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(train_data_path, transform_train)
    full_val_dataset = CustomGazeDataset(train_data_path, transform_val)
    test_dataset = CustomGazeDataset(test_data_path, transform_test)
    
    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)

    # Create dataloaders
    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader

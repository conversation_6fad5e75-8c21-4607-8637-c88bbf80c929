import cv2
import random
import numpy as np
import albumentations as A


class SafeCoarseDropout(A.DualTransform):
    def __init__(
        self,
        min_holes=1,
        max_holes=5,
        max_height_ratio=0.15,
        max_width_ratio=0.15,
        fill_value=0,   # 0, 255, or 'random'
        avoid_radius_ratio=0.1,
        always_apply=False,
        p=0.5
    ):
        super().__init__(always_apply, p)
        self.min_holes = min_holes
        self.max_holes = max_holes
        self.max_height_ratio = max_height_ratio
        self.max_width_ratio = max_width_ratio
        self.fill_value = fill_value
        self.avoid_radius_ratio = avoid_radius_ratio

    def apply(self, image, eye_coords=None, **params):
        h, w = image.shape[:2]
        
        if eye_coords is None:
            return image  # skip if no eye coordinates

        eye_px = [(int(x * w), int(y * h)) for x, y in eye_coords]
        avoid_radius = int(min(w, h) * self.avoid_radius_ratio)

        max_hole_h = int(h * self.max_height_ratio)
        max_hole_w = int(w * self.max_width_ratio)

        output = image.copy()
        num_holes = random.randint(self.min_holes, self.max_holes)

        for _ in range(num_holes):
            tries = 0
            while tries < 20:
                x = random.randint(0, w - max_hole_w)
                y = random.randint(0, h - max_hole_h)

                overlaps = False
                for ex, ey in eye_px:
                    if (
                        x - avoid_radius < ex < x + max_hole_w + avoid_radius and
                        y - avoid_radius < ey < y + max_hole_h + avoid_radius
                    ):
                        overlaps = True
                        break

                if not overlaps:
                    if self.fill_value == 'random':
                        color = np.random.randint(0, 256, size=(max_hole_h, max_hole_w, 3), dtype=np.uint8)
                    else:
                        color = np.full((max_hole_h, max_hole_w, 3), self.fill_value, dtype=np.uint8)
                    output[y:y + max_hole_h, x:x + max_hole_w] = color
                    break
                tries += 1

        return output

    def apply_to_keypoints(self, keypoints, **params):
        """
        Apply transform to keypoints. Since SafeCoarseDropout doesn't modify keypoints,
        just return them unchanged.
        """
        return keypoints

    def get_params_dependent_on_targets(self, params):
        return {"eye_coords": params.get("eye_coords", None)}

    @property
    def targets_as_params(self):
        return ["image", "eye_coords"]


def get_gaze_safe_augmentations(image_size=96):
    """ Build an traininig augmentation pipeline for gaze estimation. """
    transformer = A.Compose([
        # ----------------------------------------
        # 📦 Color & Lighting Augmentations
        A.OneOf([
            A.RandomBrightnessContrast(brightness_limit=0.1, contrast_limit=0.1, p=0.7),
            A.CLAHE(clip_limit=2.0, tile_grid_size=(8, 8), p=0.3),
        ], p=0.8),

        A.HueSaturationValue(hue_shift_limit=5, sat_shift_limit=10, val_shift_limit=10, p=0.5),

        # ----------------------------------------
        # 🔉 Noise & Blur
        A.OneOf([
            A.GaussNoise(std_range=(0.1, 0.2), p=0.6),
            A.MultiplicativeNoise(multiplier=(0.95, 1.05), p=0.4),
            A.GaussianBlur(blur_limit=3, p=0.3),
            A.MotionBlur(blur_limit=3, p=0.3),
            A.MedianBlur(blur_limit=3, p=0.2),
        ], p=0.7),

        # ----------------------------------------
        # 🎛 Channel-Based Noise
        A.OneOf([
            A.RGBShift(r_shift_limit=10, g_shift_limit=10, b_shift_limit=10, p=0.5),
            A.ChannelShuffle(p=0.3),
        ], p=0.5),

        # ----------------------------------------
        # 🕶 Partial Occlusions (Avoid eyes!)
        # A.CoarseDropout(
        #     max_holes=5,
        #     min_holes=1,
        #     max_height=int(image_size * 0.15),
        #     max_width=int(image_size * 0.15),
        #     fill_value=0,
        #     mask_fill_value=None,
        #     p=0.3,
        # ),
        SafeCoarseDropout(
            min_holes=1,
            max_holes=5,
            max_height_ratio=0.15,
            max_width_ratio=0.15,
            fill_value='random',
            avoid_radius_ratio=0.12,
            always_apply=True,
            p=1.0,
        ),
        
        # ----------------------------------------
        # 🧪 JPEG Compression Artifact Simulation
        A.ImageCompression(quality_range=(70, 90), p=0.3),

        # ----------------------------------------
        # A.Normalize(),              # mean=[0.0, 0.0, 0.0], std=[1.0, 1.0, 1.0] by default
        # A.pytorch.ToTensorV2(),     # Convert HWC to CHW and np.uint8 → torch.float32
    ], additional_targets={"eye_coords": "keypoints"})
    
    return transformer

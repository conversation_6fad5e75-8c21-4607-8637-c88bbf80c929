import os

import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

from utils import util


class CustomGazeDataset(Dataset):
    """
    PyTorch Dataset for normalized gaze tracking data.

    This dataset loads face and eye images along with corresponding gaze labels
    from a structured directory format. It supports data augmentation through
    Albumentations transforms.

    Expected directory structure:
        <data_root>/
        ├── labels.csv
        └── pXX/
            ├── <basename>-face.jpg
            ├── <basename>-left-eye.jpg
            └── <basename>-right-eye.jpg

    CSV format:
        - face_file_name: Relative path to face image
        - left_eye: Relative path to left eye image
        - right_eye: Relative path to right eye image
        - pitch: Gaze pitch angle in radians
        - yaw: Gaze yaw angle in radians

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None):
        self.data_path = data_path
        self.transform = transform
        self.df = pd.read_csv(f"{data_path}/labels.csv")
        
        self.person_ids = []
        for idx in range(len(self.df)):
            row = self.df.iloc[idx]

            # Derive person_idx from session prefix 'pXX/...'
            session = row.face_file_name.split('/')[0]
            person_idx = int(session[1:])  # 'p00' -> 0
            self.person_ids.append(person_idx)
        self.person_ids = np.array(self.person_ids)

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Derive person_idx from session prefix 'pXX/...'
        # session = row.face_file_name.split('/')[0]
        # person_idx = int(session[1:])  # 'p00' -> 0

        # Load images
        face = skimage.io.imread(f"{self.data_path}/{row.face_file_name}")
        # left = skimage.io.imread(f"{self.data_path}/{row.left_eye}")
        # right = skimage.io.imread(f"{self.data_path}/{row.right_eye}")

        # Apply transforms (optional: flipping or augmentations)
        face_tensor = self.transform(image=face)["image"]
        # left_tensor = self.transform(image=left)["image"]
        # right_tensor = self.transform(image=right)["image"]

        # Get labels
        pitch = torch.tensor(row.pitch, dtype=torch.float32)
        yaw = torch.tensor(row.yaw, dtype=torch.float32)

        return {
            # 'person_ids':      person_idx,
            'full_face_image': face_tensor,
            # 'left_eye_image':  left_tensor,
            # 'right_eye_image': right_tensor,
            'gaze_pitch':      pitch,
            'gaze_yaw':        yaw
        }


def get_dataloaders(params, val_split=0.1) -> tuple:
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function automatically splits the training data into train/validation sets
    and creates separate dataloaders with appropriate transforms. Training data
    uses augmentation while validation and test data use only normalization.

    Args:
        data_root: Root directory containing 'train' and 'test' subdirectories
        batch_size: Batch size for all dataloaders
        val_split: Fraction of training data to use for validation (0.0-1.0)
        augmentation_config: Configuration dictionary for data augmentation:
            - enabled: Whether to use data augmentation (bool)
            - shift_limit: Maximum shift as fraction of image size (float)
            - scale_limit: Maximum scale factor (float)
            - rotate_limit: Maximum rotation angle in degrees (int)
            - brightness_contrast: Whether to use brightness/contrast augmentation (bool)
            - blur: Whether to use blur augmentation (bool)

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = params.data
    batch_size = params.batch_size
    num_workers = params.num_workers

    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    transform_train = transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ])

    # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(train_data_path, transform_train)
    full_val_dataset = CustomGazeDataset(train_data_path, transform_val)
    test_dataset = CustomGazeDataset(test_data_path, transform_test)
    
    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)  # This is the length after filtering
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)

    # Create dataloaders
    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader#, full_train_dataset
